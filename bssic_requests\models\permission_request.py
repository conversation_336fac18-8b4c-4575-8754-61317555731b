from odoo import models, fields, api, _
from odoo.exceptions import UserError

class BSSICPermissionRequest(models.Model):
    _name = 'bssic.permission.request'
    _description = 'BSSIC Permission Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    name = fields.Char(string='Reference', required=True, copy=False, readonly=True,
                      default=lambda self: _('New'))

    employee_id = fields.Many2one('hr.employee', string='Employee', required=True, tracking=True)
    employee_number = fields.Char(related='employee_id.identification_id', string='Employee Number',
                                 store=True, readonly=True)
    department_id = fields.Many2one(related='employee_id.department_id', string='Department',
                                   store=True, readonly=True)

    request_date = fields.Date(string='Request Date', default=fields.Date.context_today, required=True)

    # User information
    user_name = fields.Char(string='User Name', required=True)
    job_title = fields.Char(string='Job Title', required=True)

    # Permission details
    permission_type = fields.Selection([
        ('add', 'Add'),
        ('modify', 'Modify'),
        ('delete', 'Delete'),
        ('withdraw', 'Withdraw'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
    ], string='Permission Type', required=True)

    validity_from = fields.Date(string='Valid From')
    validity_to = fields.Date(string='Valid To')

    # Department permissions
    accounting_dept = fields.Boolean(string='Accounting Department')
    accounting_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Accounting Level')
    accounting_group = fields.Char(string='Accounting Group')

    internal_audit = fields.Boolean(string='Internal Auditing')
    internal_audit_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Internal Audit Level')
    internal_audit_group = fields.Char(string='Internal Audit Group')

    risk_dept = fields.Boolean(string='Risk')
    risk_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Risk Level')
    risk_group = fields.Char(string='Risk Group')

    back_office_credits = fields.Boolean(string='Back Office - Credits')
    back_office_credits_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Credits Level')
    back_office_credits_group = fields.Char(string='Credits Group')

    back_office_deposits = fields.Boolean(string='Back Office - Deposits')
    back_office_deposits_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Deposits Level')
    back_office_deposits_group = fields.Char(string='Deposits Group')

    operations_dept = fields.Boolean(string='Operations Department')
    operations_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Operations Level')
    operations_group = fields.Char(string='Operations Group')

    forex_exchange = fields.Boolean(string='Forex Exchange')
    forex_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Forex Level')
    forex_group = fields.Char(string='Forex Group')

    banking_operations = fields.Boolean(string='Banking Operations')
    banking_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Banking Level')
    banking_group = fields.Char(string='Banking Group')

    personnel_admin = fields.Boolean(string='Personnel & Admin')
    personnel_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Personnel Level')
    personnel_group = fields.Char(string='Personnel Group')

    swift = fields.Boolean(string='Swift')
    swift_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Swift Level')
    swift_group = fields.Char(string='Swift Group')

    # Transaction limits
    transaction_amount_limit = fields.Float(string='Transaction Amount Limit')
    auth_limit = fields.Float(string='Auth. O.D. Limit')
    max_amount_limit = fields.Float(string='MAX Amount Limit')

    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager_approval', 'Direct Manager Approval'),
        ('dept_manager_approval', 'Department Manager Approval'),
        ('it_manager_approval', 'IT Manager Approval'),
        ('assigned', 'Assigned to IT Staff'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    assigned_to = fields.Many2one('hr.employee', string='Assigned To', tracking=True, ondelete="set null")

    # Activity Log
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'permission_request_id', string='Activity Log')

    @api.constrains('accounting_dept', 'accounting_level', 'risk_dept', 'risk_level',
                    'back_office_deposits', 'back_office_deposits_level', 'forex_exchange', 'forex_level',
                    'personnel_admin', 'personnel_level', 'internal_audit', 'internal_audit_level',
                    'back_office_credits', 'back_office_credits_level', 'operations_dept', 'operations_level',
                    'banking_operations', 'banking_level', 'swift', 'swift_level')
    def _check_required_levels(self):
        """التحقق من أن حقول المستويات مطلوبة عند اختيار الأقسام المحددة"""
        for record in self:
            # التحقق من Accounting Department
            if record.accounting_dept and not record.accounting_level:
                raise UserError(_('Accounting Level is required when Accounting Department is selected.'))

            # التحقق من Risk
            if record.risk_dept and not record.risk_level:
                raise UserError(_('Risk Level is required when Risk is selected.'))

            # التحقق من Back Office - Deposits
            if record.back_office_deposits and not record.back_office_deposits_level:
                raise UserError(_('Deposits Level is required when Back Office - Deposits is selected.'))

            # التحقق من Forex Exchange
            if record.forex_exchange and not record.forex_level:
                raise UserError(_('Forex Level is required when Forex Exchange is selected.'))

            # التحقق من Personnel & Admin
            if record.personnel_admin and not record.personnel_level:
                raise UserError(_('Personnel Level is required when Personnel & Admin is selected.'))

            # التحقق من Internal Auditing
            if record.internal_audit and not record.internal_audit_level:
                raise UserError(_('Internal Audit Level is required when Internal Auditing is selected.'))

            # التحقق من Back Office - Credits
            if record.back_office_credits and not record.back_office_credits_level:
                raise UserError(_('Credits Level is required when Back Office - Credits is selected.'))

            # التحقق من Operations Department
            if record.operations_dept and not record.operations_level:
                raise UserError(_('Operations Level is required when Operations Department is selected.'))

            # التحقق من Banking Operations
            if record.banking_operations and not record.banking_level:
                raise UserError(_('Banking Level is required when Banking Operations is selected.'))

            # التحقق من Swift
            if record.swift and not record.swift_level:
                raise UserError(_('Swift Level is required when Swift is selected.'))

    @api.constrains('accounting_dept', 'risk_dept', 'back_office_deposits', 'forex_exchange',
                    'personnel_admin', 'internal_audit', 'back_office_credits', 'operations_dept',
                    'banking_operations', 'swift', 'state')
    def _check_at_least_one_department_selected(self):
        """التحقق من أن المستخدم اختار على الأقل قسم واحد قبل إرسال الطلب"""
        for record in self:
            # التحقق فقط عند إرسال الطلب (ليس في حالة المسودة)
            if record.state != 'draft':
                departments_selected = [
                    record.accounting_dept,
                    record.risk_dept,
                    record.back_office_deposits,
                    record.forex_exchange,
                    record.personnel_admin,
                    record.internal_audit,
                    record.back_office_credits,
                    record.operations_dept,
                    record.banking_operations,
                    record.swift
                ]

                # التحقق من أن على الأقل قسم واحد مختار
                if not any(departments_selected):
                    raise UserError(_('You must select at least one department before submitting the permission request.'))

    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('bssic.permission.request') or _('New')
        return super(BSSICPermissionRequest, self).create(vals)

    def action_submit(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'submitted',
                notes=_('Permission request submitted for approval'),
                old_state='draft', new_state='submitted'
            )
        self.write({'state': 'submitted'})

    def action_direct_manager_approve(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Direct Manager'),
                old_state='submitted', new_state='direct_manager_approval'
            )
        self.write({'state': 'direct_manager_approval'})

    def action_dept_manager_approve(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Department Manager'),
                old_state='direct_manager_approval', new_state='dept_manager_approval'
            )
        self.write({'state': 'dept_manager_approval'})

    def action_it_manager_approve(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'it_manager_approved',
                notes=_('Approved by IT Manager'),
                old_state='dept_manager_approval', new_state='it_manager_approval'
            )
        self.write({'state': 'it_manager_approval'})

    def action_assign(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'assigned',
                notes=_('Assigned to IT Staff'),
                old_state='it_manager_approval', new_state='assigned'
            )
        self.write({'state': 'assigned'})

    def action_start_progress(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'in_progress',
                notes=_('Implementation started'),
                old_state='assigned', new_state='in_progress'
            )
        self.write({'state': 'in_progress'})

    def action_complete(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'completed',
                notes=_('Permission request completed'),
                old_state='in_progress', new_state='completed'
            )
        self.write({'state': 'completed'})

    def action_reject(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'rejected',
                notes=_('Permission request rejected'),
                old_state=self.state, new_state='rejected'
            )
        self.write({'state': 'rejected'})