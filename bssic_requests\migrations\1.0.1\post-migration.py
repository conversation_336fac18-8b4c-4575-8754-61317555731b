# -*- coding: utf-8 -*-
"""
Migration script to update permission level fields from Char to Selection
"""

def migrate(cr, version):
    """
    Convert existing permission level fields from Char to Selection values
    """
    
    # Define the mapping from old text values to new selection values
    level_mapping = {
        'user': 'user',
        'User': 'user',
        'USER': 'user',
        'clark': 'clark',
        'Clark': 'clark',
        'CLARK': 'clark',
        'verifier1': 'verifier1',
        'Verifier1': 'verifier1',
        'VERIFIER1': 'verifier1',
        'verifier2': 'verifier2',
        'Verifier2': 'verifier2',
        'VERIFIER2': 'verifier2',
        'authorizer': 'authorizer',
        'Authorizer': 'authorizer',
        'AUTHORIZER': 'authorizer',
    }
    
    # List of level fields to update
    level_fields = [
        'accounting_level',
        'internal_audit_level',
        'risk_level',
        'back_office_credits_level',
        'back_office_deposits_level',
        'operations_level',
        'forex_level',
        'banking_level',
        'personnel_level',
        'swift_level'
    ]
    
    # Update bssic_request table
    for field in level_fields:
        # Get all unique values for this field
        cr.execute(f"""
            SELECT DISTINCT {field} 
            FROM bssic_request 
            WHERE {field} IS NOT NULL AND {field} != ''
        """)
        
        existing_values = [row[0] for row in cr.fetchall()]
        
        # Update each value according to the mapping
        for old_value in existing_values:
            if old_value in level_mapping:
                new_value = level_mapping[old_value]
                cr.execute(f"""
                    UPDATE bssic_request 
                    SET {field} = %s 
                    WHERE {field} = %s
                """, (new_value, old_value))
            else:
                # For unmapped values, set to NULL or default
                cr.execute(f"""
                    UPDATE bssic_request 
                    SET {field} = NULL 
                    WHERE {field} = %s
                """, (old_value,))
    
    # Update bssic_permission_request table if it exists
    cr.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'bssic_permission_request'
        )
    """)
    
    if cr.fetchone()[0]:
        for field in level_fields:
            # Get all unique values for this field
            cr.execute(f"""
                SELECT DISTINCT {field} 
                FROM bssic_permission_request 
                WHERE {field} IS NOT NULL AND {field} != ''
            """)
            
            existing_values = [row[0] for row in cr.fetchall()]
            
            # Update each value according to the mapping
            for old_value in existing_values:
                if old_value in level_mapping:
                    new_value = level_mapping[old_value]
                    cr.execute(f"""
                        UPDATE bssic_permission_request 
                        SET {field} = %s 
                        WHERE {field} = %s
                    """, (new_value, old_value))
                else:
                    # For unmapped values, set to NULL
                    cr.execute(f"""
                        UPDATE bssic_permission_request 
                        SET {field} = NULL 
                        WHERE {field} = %s
                    """, (old_value,))
    
    # Log the migration
    print("Migration completed: Permission level fields converted to selection values")
